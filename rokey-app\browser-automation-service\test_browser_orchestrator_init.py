#!/usr/bin/env python3
"""
Test browser orchestrator initialization in isolation
"""

import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_browser_orchestrator_init():
    """Test browser orchestrator initialization"""
    print("🧪 Testing browser orchestrator initialization...")
    
    try:
        from app.models.browser_automation import BrowserAutomationRequest
        from app.services.browser_orchestrator import BrowserOrchestrator
        from app.services.session_manager import SessionManager
        
        # Create test request
        test_data = {
            "task": "when is <PERSON><PERSON>'s next match?",
            "task_type": "data_extraction",
            "user_id": "69d967d5-0b7b-402b-ae1b-711d9b74eef4",
            "config_id": "b39270c0-feb8-4c99-b6d2-9ee224edd57e",
            "config_name": "openrouter",
            "user_tier": "professional",
            "extracted_parameters": {},
            "api_keys": [
                {
                    "id": "94373577-9efb-4a88-8eba-490625742d16",
                    "provider": "openrouter",
                    "model": "microsoft/phi-4-reasoning:free",
                    "api_key": "sk-or-v1-test_key_for_testing_purposes_only",
                    "temperature": 1,
                    "label": "llama",
                    "roles": []
                }
            ],
            "browser_headless": True,
            "browser_viewport_width": 1920,
            "browser_viewport_height": 1080,
            "browser_slow_mo": 0,
            "browser_devtools": False,
            "stream": True
        }
        
        print("🔄 Creating request model...")
        request = BrowserAutomationRequest(**test_data)
        print("✅ Request model created!")
        
        print("🔄 Creating session manager...")
        session_manager = SessionManager()
        print("✅ Session manager created!")
        
        print("🔄 Creating browser orchestrator...")
        task_id = "test-task-123"
        orchestrator = BrowserOrchestrator(
            task_id=task_id,
            request=request,
            session_manager=session_manager,
            enable_streaming=True
        )
        print("✅ Browser orchestrator created!")
        
        print("🔄 Testing initialization...")
        try:
            await orchestrator._initialize_execution()
            print("✅ Browser orchestrator initialization successful!")
            return True
        except Exception as init_error:
            print(f"❌ Initialization failed: {init_error}")
            import traceback
            print(f"📋 Init error traceback: {traceback.format_exc()}")
            return False
        
    except Exception as e:
        print(f"❌ Browser orchestrator test failed: {e}")
        import traceback
        print(f"📋 Full traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    asyncio.run(test_browser_orchestrator_init())
