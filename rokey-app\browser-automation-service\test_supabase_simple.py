#!/usr/bin/env python3
"""
Simple test to check if Supabase client is working
"""

import os
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_supabase_client():
    """Test basic Supabase client functionality"""
    try:
        print("🧪 Testing Supabase client...")
        
        # Import Supabase
        from supabase import create_client, Client
        
        supabase_url = os.getenv("SUPABASE_URL", "https://hpkzzhpufhbxtxqaugjh.supabase.co")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        
        print(f"📋 Supabase URL: {supabase_url}")
        print(f"📋 Service key configured: {'Yes' if supabase_key else 'No'}")
        
        if not supabase_key:
            print("❌ SUPABASE_SERVICE_ROLE_KEY not configured")
            return
        
        # Create client
        print("🔄 Creating Supabase client...")
        supabase: Client = create_client(supabase_url, supabase_key)
        print("✅ Supabase client created successfully")
        
        # Test a simple query
        print("🔄 Testing simple query...")
        try:
            # Try to query a table that should exist
            response = supabase.table('custom_api_configs').select('id').limit(1).execute()
            print(f"✅ Query successful. Response type: {type(response)}")
            print(f"📋 Response data type: {type(response.data)}")
            print(f"📋 Response data: {response.data}")
        except Exception as query_error:
            print(f"❌ Query failed: {query_error}")
            import traceback
            print(f"📋 Full traceback: {traceback.format_exc()}")
        
    except Exception as e:
        print(f"❌ Supabase client test failed: {e}")
        import traceback
        print(f"📋 Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_supabase_client())
