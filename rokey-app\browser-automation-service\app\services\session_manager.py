"""
Session Manager
Advanced browser session pooling and management for optimal performance
"""

import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Set
from enum import Enum
from dataclasses import dataclass, field
import uuid
import psutil
import weakref

from app.core.logging import LoggerMixin
from app.core.exceptions import BrowserAutomationException


class SessionState(Enum):
    """Browser session states"""
    INITIALIZING = "initializing"
    IDLE = "idle"
    BUSY = "busy"
    WARMING_UP = "warming_up"
    COOLING_DOWN = "cooling_down"
    ERROR = "error"
    TERMINATED = "terminated"


class SessionPriority(Enum):
    """Session priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class SessionConfig:
    """Configuration for browser sessions"""
    headless: bool = True
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    viewport_width: int = 1920
    viewport_height: int = 1080
    timeout_seconds: int = 30
    max_pages: int = 5
    enable_javascript: bool = True
    enable_images: bool = False
    enable_css: bool = True
    proxy_url: Optional[str] = None
    custom_headers: Dict[str, str] = field(default_factory=dict)
    browser_args: List[str] = field(default_factory=list)


@dataclass
class SessionMetrics:
    """Session performance metrics"""
    session_id: str
    created_at: datetime
    last_used: datetime
    total_tasks: int = 0
    successful_tasks: int = 0
    failed_tasks: int = 0
    total_runtime: float = 0.0
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    network_requests: int = 0
    pages_loaded: int = 0
    errors_count: int = 0


@dataclass
class BrowserSession:
    """Browser session wrapper"""
    session_id: str
    user_id: str
    state: SessionState
    priority: SessionPriority
    config: SessionConfig
    created_at: datetime
    last_used: datetime
    browser_instance: Any = None
    active_pages: List[Any] = field(default_factory=list)
    task_queue: List[str] = field(default_factory=list)
    metrics: SessionMetrics = None
    warmup_completed: bool = False
    max_idle_time: float = 300.0  # 5 minutes
    max_lifetime: float = 3600.0  # 1 hour

    def __post_init__(self):
        if self.metrics is None:
            self.metrics = SessionMetrics(
                session_id=self.session_id,
                created_at=self.created_at,
                last_used=self.last_used
            )


class SessionManager(LoggerMixin):
    """
    Advanced browser session management system

    Features:
    - Intelligent session pooling with warm-up
    - Concurrent task handling with load balancing
    - Resource monitoring and optimization
    - Automatic session lifecycle management
    - Performance analytics and optimization
    - Memory and CPU usage tracking
    - Session affinity and user isolation
    - Graceful degradation under load
    """

    def __init__(self, max_sessions: int = 10, max_sessions_per_user: int = 3):
        # Session pool management
        self.max_sessions = max_sessions
        self.max_sessions_per_user = max_sessions_per_user
        self.active_sessions: Dict[str, BrowserSession] = {}
        self.user_sessions: Dict[str, Set[str]] = {}
        self.session_queue: asyncio.Queue = asyncio.Queue()

        # Performance monitoring
        self.performance_metrics = {
            "total_sessions_created": 0,
            "total_sessions_destroyed": 0,
            "current_active_sessions": 0,
            "peak_concurrent_sessions": 0,
            "average_session_lifetime": 0.0,
            "total_tasks_processed": 0,
            "average_task_duration": 0.0,
            "memory_usage_mb": 0.0,
            "cpu_usage_percent": 0.0
        }

        # Resource limits and thresholds
        self.resource_limits = {
            "max_memory_mb": 2048,  # 2GB per session
            "max_cpu_percent": 80,
            "max_concurrent_tasks": 50,
            "session_warmup_timeout": 30.0,
            "task_timeout": 300.0
        }

        # Session optimization settings
        self.optimization_config = {
            "enable_session_reuse": True,
            "enable_warmup": True,
            "enable_preemptive_scaling": True,
            "enable_resource_monitoring": True,
            "cleanup_interval": 60.0,
            "metrics_collection_interval": 30.0
        }

        # Background tasks
        self._cleanup_task = None
        self._monitoring_task = None
        self._optimization_task = None

        self.log_info("Session manager initialized", max_sessions=max_sessions)

    async def initialize(self):
        """Initialize session manager and start background tasks"""
        try:
            self.log_info("Initializing session manager")

            # Initialize browser pool first
            from app.services.browser_pool import browser_pool
            await browser_pool.initialize()

            # Start background tasks
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())
            self._optimization_task = asyncio.create_task(self._optimization_loop())

            # Pre-warm initial sessions if enabled
            if self.optimization_config["enable_warmup"]:
                await self._prewarm_sessions(2)  # Pre-warm 2 sessions

            self.log_info("Session manager initialized successfully")

        except Exception as e:
            self.log_error(f"Session manager initialization failed: {e}")
            raise

    async def get_session(
        self,
        user_id: str,
        priority: SessionPriority = SessionPriority.NORMAL,
        config: Optional[SessionConfig] = None,
        task_id: Optional[str] = None
    ) -> BrowserSession:
        """
        Get an available browser session for the user

        Args:
            user_id: User identifier
            priority: Session priority level
            config: Optional session configuration
            task_id: Optional task identifier

        Returns:
            Available browser session
        """
        try:
            self.log_info(f"Getting session for user: {user_id}", priority=priority.value)

            # Check user session limits
            user_session_count = len(self.user_sessions.get(user_id, set()))
            if user_session_count >= self.max_sessions_per_user:
                # Try to reuse existing session
                reused_session = await self._try_reuse_session(user_id, priority)
                if reused_session:
                    return reused_session

                # Wait for available session
                await self._wait_for_available_session(user_id)

            # Check global session limits
            if len(self.active_sessions) >= self.max_sessions:
                # Try to optimize existing sessions
                await self._optimize_session_pool()

                # If still at limit, wait or reject based on priority
                if len(self.active_sessions) >= self.max_sessions:
                    if priority in [SessionPriority.HIGH, SessionPriority.CRITICAL]:
                        await self._evict_low_priority_session()
                    else:
                        raise BrowserAutomationException("Session pool at capacity")

            # Create new session
            session = await self._create_session(user_id, priority, config or SessionConfig())

            # Add task to session queue if provided
            if task_id:
                session.task_queue.append(task_id)

            self.log_info(f"Session allocated: {session.session_id}", user_id=user_id)

            return session

        except Exception as e:
            self.log_error(f"Failed to get session: {e}")
            raise

    async def release_session(
        self,
        session_id: str,
        task_id: Optional[str] = None,
        keep_alive: bool = True
    ):
        """
        Release a browser session after task completion

        Args:
            session_id: Session identifier
            task_id: Optional task identifier
            keep_alive: Whether to keep session alive for reuse
        """
        try:
            if session_id not in self.active_sessions:
                self.log_warning(f"Session not found for release: {session_id}")
                return

            session = self.active_sessions[session_id]

            # Remove task from queue
            if task_id and task_id in session.task_queue:
                session.task_queue.remove(task_id)

            # Update session state
            if not session.task_queue:
                session.state = SessionState.IDLE
                session.last_used = datetime.now()

            # Update metrics
            session.metrics.total_tasks += 1
            session.metrics.last_used = datetime.now()

            # Decide whether to keep session alive
            if keep_alive and self._should_keep_session_alive(session):
                self.log_info(f"Session kept alive: {session_id}")
            else:
                await self._terminate_session(session_id)
                self.log_info(f"Session terminated: {session_id}")

        except Exception as e:
            self.log_error(f"Failed to release session: {e}")

    async def execute_task_with_session(
        self,
        user_id: str,
        task_function: callable,
        task_args: Dict[str, Any],
        priority: SessionPriority = SessionPriority.NORMAL,
        config: Optional[SessionConfig] = None
    ) -> Any:
        """
        Execute a task with automatic session management

        Args:
            user_id: User identifier
            task_function: Function to execute
            task_args: Arguments for the task function
            priority: Task priority
            config: Optional session configuration

        Returns:
            Task execution result
        """
        try:
            task_id = str(uuid.uuid4())
            start_time = datetime.now()

            self.log_info(f"Executing task with session: {task_id}", user_id=user_id)

            # Get session
            session = await self.get_session(user_id, priority, config, task_id)

            try:
                # Update session state
                session.state = SessionState.BUSY

                # Execute task
                result = await task_function(session, **task_args)

                # Update success metrics
                session.metrics.successful_tasks += 1
                execution_time = (datetime.now() - start_time).total_seconds()
                session.metrics.total_runtime += execution_time

                self.log_info(f"Task completed successfully: {task_id}", execution_time=execution_time)

                return result

            except Exception as e:
                # Update failure metrics
                session.metrics.failed_tasks += 1
                session.metrics.errors_count += 1

                self.log_error(f"Task execution failed: {task_id}", error=str(e))
                raise

            finally:
                # Release session
                await self.release_session(session.session_id, task_id)

        except Exception as e:
            self.log_error(f"Task execution with session failed: {e}")
            raise

    async def get_session_metrics(self, session_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get session performance metrics

        Args:
            session_id: Optional specific session ID

        Returns:
            Session metrics data
        """
        try:
            if session_id:
                if session_id not in self.active_sessions:
                    return {"error": f"Session not found: {session_id}"}

                session = self.active_sessions[session_id]
                return {
                    "session_id": session_id,
                    "user_id": session.user_id,
                    "state": session.state.value,
                    "priority": session.priority.value,
                    "created_at": session.created_at.isoformat(),
                    "last_used": session.last_used.isoformat(),
                    "lifetime_seconds": (datetime.now() - session.created_at).total_seconds(),
                    "idle_time_seconds": (datetime.now() - session.last_used).total_seconds(),
                    "active_pages": len(session.active_pages),
                    "queued_tasks": len(session.task_queue),
                    "metrics": {
                        "total_tasks": session.metrics.total_tasks,
                        "successful_tasks": session.metrics.successful_tasks,
                        "failed_tasks": session.metrics.failed_tasks,
                        "success_rate": (session.metrics.successful_tasks / max(session.metrics.total_tasks, 1)) * 100,
                        "total_runtime": session.metrics.total_runtime,
                        "average_task_time": session.metrics.total_runtime / max(session.metrics.total_tasks, 1),
                        "memory_usage_mb": session.metrics.memory_usage_mb,
                        "cpu_usage_percent": session.metrics.cpu_usage_percent,
                        "network_requests": session.metrics.network_requests,
                        "pages_loaded": session.metrics.pages_loaded,
                        "errors_count": session.metrics.errors_count
                    }
                }
            else:
                # Return overall metrics
                return {
                    "pool_metrics": self.performance_metrics.copy(),
                    "active_sessions": len(self.active_sessions),
                    "sessions_by_state": await self._get_sessions_by_state(),
                    "sessions_by_user": {
                        user_id: len(sessions)
                        for user_id, sessions in self.user_sessions.items()
                    },
                    "resource_usage": await self._get_resource_usage(),
                    "optimization_status": await self._get_optimization_status()
                }

        except Exception as e:
            self.log_error(f"Failed to get session metrics: {e}")
            return {"error": str(e)}

    async def optimize_session_pool(self) -> Dict[str, Any]:
        """
        Manually trigger session pool optimization

        Returns:
            Optimization results
        """
        try:
            self.log_info("Manual session pool optimization triggered")

            optimization_results = {
                "sessions_before": len(self.active_sessions),
                "sessions_terminated": 0,
                "sessions_optimized": 0,
                "memory_freed_mb": 0.0,
                "optimization_time": 0.0
            }

            start_time = datetime.now()

            # Terminate idle sessions
            terminated_count = await self._cleanup_idle_sessions()
            optimization_results["sessions_terminated"] = terminated_count

            # Optimize remaining sessions
            optimized_count = await self._optimize_active_sessions()
            optimization_results["sessions_optimized"] = optimized_count

            # Calculate memory freed (estimated)
            optimization_results["memory_freed_mb"] = terminated_count * 200  # Estimate 200MB per session

            optimization_results["sessions_after"] = len(self.active_sessions)
            optimization_results["optimization_time"] = (datetime.now() - start_time).total_seconds()

            self.log_info("Session pool optimization completed", results=optimization_results)

            return optimization_results

        except Exception as e:
            self.log_error(f"Session pool optimization failed: {e}")
            return {"error": str(e)}

    async def register_task(self, task_id: str, user_id: str, task_metadata: Dict[str, Any]):
        """Register a new task for tracking"""
        try:
            self.log_info(f"Registering task: {task_id}", user_id=user_id, metadata=task_metadata)
            # For now, just log the task registration
            # In a full implementation, this would store task metadata for tracking

        except Exception as e:
            self.log_error(f"Failed to register task: {e}")

    async def update_task_status(self, task_id: str, status: Any, metadata: Optional[Dict[str, Any]] = None):
        """Update task status"""
        try:
            self.log_info(f"Updating task status: {task_id}", status=str(status), metadata=metadata)
            # For now, just log the status update
            # In a full implementation, this would update task status in storage

        except Exception as e:
            self.log_error(f"Failed to update task status: {e}")

    async def create_session(self, user_id: str, task_id: str, browser_config: Dict[str, Any]) -> Any:
        """Create a browser session for a task"""
        try:
            self.log_info(f"Creating session for task: {task_id}", user_id=user_id, config=browser_config)

            # Convert browser config to SessionConfig
            session_config = SessionConfig(
                headless=browser_config.get("headless", True),
                viewport_width=browser_config.get("viewport_width", 1920),
                viewport_height=browser_config.get("viewport_height", 1080),
                timeout_seconds=30
            )

            # Create session using existing method
            session = await self.get_session(user_id, SessionPriority.NORMAL, session_config, task_id)

            return session

        except Exception as e:
            self.log_error(f"Failed to create session: {e}")
            # Return a mock session object for development
            return type('MockSession', (), {
                'session_id': f'mock_{task_id}',
                'user_id': user_id,
                'browser_instance': None
            })()

    async def increment_usage(self, user_id: str):
        """Increment usage tracking for user"""
        try:
            self.log_info(f"Incrementing usage for user: {user_id}")
            # For now, just log the usage increment
            # In a full implementation, this would update usage statistics

        except Exception as e:
            self.log_error(f"Failed to increment usage: {e}")

    async def shutdown(self):
        """Shutdown session manager and cleanup resources"""
        try:
            self.log_info("Shutting down session manager")

            # Cancel background tasks
            if self._cleanup_task:
                self._cleanup_task.cancel()
            if self._monitoring_task:
                self._monitoring_task.cancel()
            if self._optimization_task:
                self._optimization_task.cancel()

            # Terminate all active sessions
            session_ids = list(self.active_sessions.keys())
            for session_id in session_ids:
                await self._terminate_session(session_id)

            self.log_info("Session manager shutdown completed")

        except Exception as e:
            self.log_error(f"Session manager shutdown failed: {e}")

    # Helper methods for session management
    async def _create_session(
        self,
        user_id: str,
        priority: SessionPriority,
        config: SessionConfig
    ) -> BrowserSession:
        """Create a new browser session"""
        try:
            session_id = str(uuid.uuid4())

            # Create session object
            session = BrowserSession(
                session_id=session_id,
                user_id=user_id,
                state=SessionState.INITIALIZING,
                priority=priority,
                config=config,
                created_at=datetime.now(),
                last_used=datetime.now()
            )

            # Get real browser instance from browser pool
            from app.services.browser_pool import browser_pool
            session.browser_instance = await browser_pool.get_browser_instance(session_id)

            # Warm up session if enabled
            if self.optimization_config["enable_warmup"]:
                await self._warmup_session(session)

            # Add to active sessions
            self.active_sessions[session_id] = session

            # Track user sessions
            if user_id not in self.user_sessions:
                self.user_sessions[user_id] = set()
            self.user_sessions[user_id].add(session_id)

            # Update metrics
            self.performance_metrics["total_sessions_created"] += 1
            self.performance_metrics["current_active_sessions"] = len(self.active_sessions)
            self.performance_metrics["peak_concurrent_sessions"] = max(
                self.performance_metrics["peak_concurrent_sessions"],
                len(self.active_sessions)
            )

            session.state = SessionState.IDLE

            self.log_info(f"Session created: {session_id}", user_id=user_id)

            return session

        except Exception as e:
            self.log_error(f"Session creation failed: {e}")
            raise



    async def _warmup_session(self, session: BrowserSession):
        """Warm up session for better performance with real browser operations"""
        try:
            session.state = SessionState.WARMING_UP

            # Real warmup activities with browser instance
            if session.browser_instance:
                # Create a context for warmup (viewport is already set in browser_instance.create_context)
                context = await session.browser_instance.create_context(
                    f"warmup_{session.session_id}"
                )

                # Create a page and navigate to a simple page to warm up the browser
                page = await context.new_page()
                await page.goto('about:blank')
                await page.close()

                # Close warmup context
                await session.browser_instance.close_context(f"warmup_{session.session_id}")

            session.warmup_completed = True
            self.log_info(f"Session warmed up with real browser operations: {session.session_id}")

        except Exception as e:
            self.log_error(f"Session warmup failed: {e}")
            # Don't fail session creation if warmup fails
            session.warmup_completed = False

    async def _terminate_session(self, session_id: str):
        """Terminate a browser session"""
        try:
            if session_id not in self.active_sessions:
                return

            session = self.active_sessions[session_id]
            session.state = SessionState.TERMINATED

            # Close browser instance properly
            if session.browser_instance:
                try:
                    # Close all contexts for this session
                    for context_id in list(session.browser_instance.contexts.keys()):
                        await session.browser_instance.close_context(context_id)

                    # Release browser instance back to pool
                    from app.services.browser_pool import browser_pool
                    await browser_pool.release_browser_instance(session.browser_instance.instance_id)
                except Exception as e:
                    self.log_error(f"Error closing browser instance: {e}")

            # Remove from tracking
            del self.active_sessions[session_id]

            if session.user_id in self.user_sessions:
                self.user_sessions[session.user_id].discard(session_id)
                if not self.user_sessions[session.user_id]:
                    del self.user_sessions[session.user_id]

            # Update metrics
            self.performance_metrics["total_sessions_destroyed"] += 1
            self.performance_metrics["current_active_sessions"] = len(self.active_sessions)

            self.log_info(f"Session terminated: {session_id}")

        except Exception as e:
            self.log_error(f"Session termination failed: {e}")

    def _should_keep_session_alive(self, session: BrowserSession) -> bool:
        """Determine if session should be kept alive"""
        try:
            # Check idle time
            idle_time = (datetime.now() - session.last_used).total_seconds()
            if idle_time > session.max_idle_time:
                return False

            # Check lifetime
            lifetime = (datetime.now() - session.created_at).total_seconds()
            if lifetime > session.max_lifetime:
                return False

            # Check error rate
            if session.metrics.total_tasks > 0:
                error_rate = session.metrics.failed_tasks / session.metrics.total_tasks
                if error_rate > 0.5:  # 50% error rate
                    return False

            return True

        except Exception:
            return False

    async def _try_reuse_session(self, user_id: str, priority: SessionPriority) -> Optional[BrowserSession]:
        """Try to reuse existing session for user"""
        try:
            user_sessions = self.user_sessions.get(user_id, set())

            for session_id in user_sessions:
                if session_id in self.active_sessions:
                    session = self.active_sessions[session_id]

                    if (session.state == SessionState.IDLE and
                        session.priority.value <= priority.value):
                        return session

            return None

        except Exception as e:
            self.log_error(f"Session reuse failed: {e}")
            return None

    async def _wait_for_available_session(self, user_id: str):
        """Wait for session to become available"""
        try:
            # Simple wait implementation
            await asyncio.sleep(1.0)

        except Exception as e:
            self.log_error(f"Wait for session failed: {e}")

    async def _optimize_session_pool(self):
        """Optimize session pool by cleaning up idle sessions"""
        try:
            await self._cleanup_idle_sessions()

        except Exception as e:
            self.log_error(f"Session pool optimization failed: {e}")

    async def _evict_low_priority_session(self):
        """Evict lowest priority session"""
        try:
            lowest_priority_session = None
            lowest_priority = SessionPriority.CRITICAL.value

            for session in self.active_sessions.values():
                if (session.state == SessionState.IDLE and
                    session.priority.value < lowest_priority):
                    lowest_priority = session.priority.value
                    lowest_priority_session = session

            if lowest_priority_session:
                await self._terminate_session(lowest_priority_session.session_id)

        except Exception as e:
            self.log_error(f"Session eviction failed: {e}")

    async def _prewarm_sessions(self, count: int):
        """Pre-warm sessions for better performance"""
        try:
            for i in range(count):
                session = await self._create_session(
                    user_id="system",
                    priority=SessionPriority.LOW,
                    config=SessionConfig()
                )
                self.log_info(f"Pre-warmed session: {session.session_id}")

        except Exception as e:
            self.log_error(f"Session pre-warming failed: {e}")

    # Background task methods
    async def _cleanup_loop(self):
        """Background cleanup loop"""
        while True:
            try:
                await asyncio.sleep(self.optimization_config["cleanup_interval"])
                await self._cleanup_idle_sessions()

            except asyncio.CancelledError:
                break
            except Exception as e:
                self.log_error(f"Cleanup loop error: {e}")

    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while True:
            try:
                await asyncio.sleep(self.optimization_config["metrics_collection_interval"])
                await self._collect_metrics()

            except asyncio.CancelledError:
                break
            except Exception as e:
                self.log_error(f"Monitoring loop error: {e}")

    async def _optimization_loop(self):
        """Background optimization loop"""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                await self._optimize_active_sessions()

            except asyncio.CancelledError:
                break
            except Exception as e:
                self.log_error(f"Optimization loop error: {e}")

    async def _cleanup_idle_sessions(self) -> int:
        """Clean up idle sessions"""
        try:
            current_time = datetime.now()
            idle_sessions = []

            for session_id, session in self.active_sessions.items():
                idle_time = (current_time - session.last_used).total_seconds()
                if idle_time > session.max_idle_time:
                    idle_sessions.append(session_id)

            for session_id in idle_sessions:
                await self._terminate_session(session_id)

            return len(idle_sessions)

        except Exception as e:
            self.log_error(f"Idle session cleanup failed: {e}")
            return 0

    async def _optimize_active_sessions(self) -> int:
        """Optimize active sessions"""
        try:
            optimized_count = 0

            for session in self.active_sessions.values():
                if session.state == SessionState.IDLE:
                    # Perform real optimization: clear browser cache, reset contexts
                    try:
                        if session.browser_instance:
                            # Clear any unused contexts
                            for context_id in list(session.browser_instance.contexts.keys()):
                                if context_id.startswith('temp_') or context_id.startswith('warmup_'):
                                    await session.browser_instance.close_context(context_id)
                        optimized_count += 1
                    except Exception as e:
                        self.log_error(f"Session optimization failed for {session.session_id}: {e}")

            return optimized_count

        except Exception as e:
            self.log_error(f"Session optimization failed: {e}")
            return 0

    async def _collect_metrics(self):
        """Collect performance metrics"""
        try:
            # Update resource usage
            process = psutil.Process()
            self.performance_metrics["memory_usage_mb"] = process.memory_info().rss / 1024 / 1024
            self.performance_metrics["cpu_usage_percent"] = process.cpu_percent()

        except Exception as e:
            self.log_error(f"Metrics collection failed: {e}")

    async def _get_sessions_by_state(self) -> Dict[str, int]:
        """Get session count by state"""
        try:
            state_counts = {}
            for session in self.active_sessions.values():
                state = session.state.value
                state_counts[state] = state_counts.get(state, 0) + 1
            return state_counts

        except Exception:
            return {}

    async def _get_resource_usage(self) -> Dict[str, float]:
        """Get current resource usage"""
        try:
            process = psutil.Process()
            return {
                "memory_mb": process.memory_info().rss / 1024 / 1024,
                "cpu_percent": process.cpu_percent(),
                "open_files": len(process.open_files()),
                "connections": len(process.connections())
            }
        except Exception:
            return {}

    async def _get_optimization_status(self) -> Dict[str, Any]:
        """Get optimization status"""
        return {
            "session_reuse_enabled": self.optimization_config["enable_session_reuse"],
            "warmup_enabled": self.optimization_config["enable_warmup"],
            "resource_monitoring_enabled": self.optimization_config["enable_resource_monitoring"],
            "cleanup_interval": self.optimization_config["cleanup_interval"],
            "metrics_interval": self.optimization_config["metrics_collection_interval"]
        }


# Global session manager instance
session_manager = SessionManager()
