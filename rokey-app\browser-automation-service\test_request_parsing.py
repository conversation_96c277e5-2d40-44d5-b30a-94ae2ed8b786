#!/usr/bin/env python3
"""
Test FastAPI request parsing in isolation
"""

import asyncio
import json
from app.models.browser_automation import BrowserAutomationRequest

async def test_request_parsing():
    """Test if the request parsing works correctly"""
    print("🧪 Testing FastAPI request parsing...")
    
    # Test data from the debug script
    test_data = {
        "task": "when is <PERSON><PERSON>'s next match?",
        "task_type": "data_extraction",
        "user_id": "69d967d5-0b7b-402b-ae1b-711d9b74eef4",
        "config_id": "b39270c0-feb8-4c99-b6d2-9ee224edd57e",
        "config_name": "openrouter",
        "user_tier": "professional",
        "extracted_parameters": {},
        "api_keys": [
            {
                "id": "94373577-9efb-4a88-8eba-490625742d16",
                "provider": "openrouter",
                "model": "microsoft/phi-4-reasoning:free",
                "api_key": "sk-or-v1-test_key_for_testing_purposes_only",
                "temperature": 1,
                "label": "llama",
                "roles": []
            }
        ],
        "browser_headless": True,
        "browser_viewport_width": 1920,
        "browser_viewport_height": 1080,
        "browser_slow_mo": 0,
        "browser_devtools": False,
        "stream": True
    }
    
    try:
        # Try to create the Pydantic model
        print("🔄 Creating BrowserAutomationRequest model...")
        request = BrowserAutomationRequest(**test_data)
        print("✅ Request parsing successful!")
        print(f"📋 User tier: {request.user_tier}")
        print(f"📋 User tier type: {type(request.user_tier)}")
        print(f"📋 API keys count: {len(request.api_keys)}")
        return True
        
    except Exception as e:
        print(f"❌ Request parsing failed: {e}")
        import traceback
        print(f"📋 Full traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    asyncio.run(test_request_parsing())
