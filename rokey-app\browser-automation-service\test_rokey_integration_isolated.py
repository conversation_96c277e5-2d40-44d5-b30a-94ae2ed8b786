#!/usr/bin/env python3
"""
Test RouKey integration in isolation
"""

import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_rokey_integration():
    """Test RouKey integration components individually"""
    print("🧪 Testing RouKey integration...")
    
    try:
        from app.services.rokey_integration import RouKeyIntegration
        
        # Create integration instance
        print("🔄 Creating RouKey integration instance...")
        rokey_integration = RouKeyIntegration()
        print("✅ RouKey integration instance created!")
        
        # Test user subscription
        print("🔄 Testing get_user_subscription...")
        user_id = "69d967d5-0b7b-402b-ae1b-711d9b74eef4"
        subscription = await rokey_integration.get_user_subscription(user_id)
        print(f"✅ User subscription: {subscription}")
        
        # Test user config
        print("🔄 Testing get_user_config...")
        config_id = "b39270c0-feb8-4c99-b6d2-9ee224edd57e"
        try:
            user_config = await rokey_integration.get_user_config(user_id, config_id)
            print(f"✅ User config retrieved: {type(user_config)}")
            print(f"📋 Config keys: {list(user_config.keys()) if isinstance(user_config, dict) else 'Not a dict'}")
        except Exception as config_error:
            print(f"❌ User config failed: {config_error}")
            import traceback
            print(f"📋 Config error traceback: {traceback.format_exc()}")
        
        # Test role classification
        print("🔄 Testing classify_task_roles...")
        try:
            api_keys = [
                {
                    "id": "94373577-9efb-4a88-8eba-490625742d16",
                    "provider": "openrouter",
                    "model": "microsoft/phi-4-reasoning:free",
                    "api_key": "sk-or-v1-test_key_for_testing_purposes_only",
                    "temperature": 1,
                    "label": "llama",
                    "roles": []
                }
            ]
            roles = await rokey_integration.classify_task_roles(
                "when is Messi's next match?",
                {"roles": {}},
                api_keys
            )
            print(f"✅ Role classification: {roles}")
        except Exception as role_error:
            print(f"❌ Role classification failed: {role_error}")
            import traceback
            print(f"📋 Role error traceback: {traceback.format_exc()}")
        
        # Clean up
        await rokey_integration.close()
        print("✅ RouKey integration test completed!")
        return True
        
    except Exception as e:
        print(f"❌ RouKey integration test failed: {e}")
        import traceback
        print(f"📋 Full traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    asyncio.run(test_rokey_integration())
